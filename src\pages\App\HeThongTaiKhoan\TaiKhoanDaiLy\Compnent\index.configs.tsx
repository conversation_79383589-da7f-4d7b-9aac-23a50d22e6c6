import {IFormInput} from "@src/@types";
import {REGUlAR_EXPRESSION} from "@src/constants";
import {colWidthByKey, defaultTableColumnsProps} from "@src/hooks";
import {parseDateTime} from "@src/utils";
import {TableProps} from "antd";

export const initFormFields = (form: any, chiTietTaiKhoanDaiLy: any) => {
  if (!chiTietTaiKhoanDaiLy) return;

  const fields = Object.entries(chiTietTaiKhoanDaiLy).map(([name, value]) => {
    const DATE_TIME_FIELDS = ["ngay_hl", "ngay_kt", "ngay_sinh"];
    if (DATE_TIME_FIELDS.includes(name)) {
      if (value) {
        return {
          name,
          value: parseDateTime(value),
        };
      }
    }
    return {
      name,
      value,
    };
  });

  form.setFields(fields);
};

const ruleRequired = {
  required: true,
  message: "Thông tin bắt buộc",
};
export interface IFormChiTietTaiKhoanDaiLyFieldsConfig {
  ma_doi_tac: IFormInput;
  ma: IFormInput;
  ten: IFormInput;
  ma_dly: IFormInput;
  mat_khau: IFormInput;
  dthoai: IFormInput;
  ngay_hl: IFormInput;
  ngay_kt: IFormInput;
  email: IFormInput;
  trang_thai: IFormInput;
  ngay_sinh: IFormInput;
  gioi_tinh: IFormInput;
  cmt: IFormInput;
}
// const FormChiTietDanhMucSanPham: IFormChiTietDanhMucSanPhamFieldsConfig = {
export const FormChiTietTaiKhoanDaiLy: IFormChiTietTaiKhoanDaiLyFieldsConfig = {
  ten: {
    component: "input",
    name: "ten",
    placeholder: "Tên người dùng",
    label: "Tên người dùng", // cho label vào thì sẽ thành input with label
    rules: [ruleRequired],
  },
  dthoai: {
    component: "input",
    name: "dthoai",
    placeholder: "Điện thoại",
    label: "Điện thoại", // cho label vào thì sẽ thành input with label
    rules: [
      ruleRequired,
      {
        pattern: REGUlAR_EXPRESSION.REG_PHONE,
        message: "Số điện thoại sai định dạng",
      },
    ],
  },
  email: {
    component: "input",
    name: "email",
    placeholder: "Email",
    label: "Email", // cho label vào thì sẽ thành input with label
    rules: [
      ruleRequired,
      {
        pattern: REGUlAR_EXPRESSION.REG_EMAIL,
        message: "Email sai định dạng",
      },
    ],
  },
  ma: {
    component: "input",
    name: "ma",
    placeholder: "Nhập mã người dùng",
    label: "Mã người dùng", // cho label vào thì sẽ thành input with label
    rules: [ruleRequired],
  },
  mat_khau: {
    component: "input",
    name: "mat_khau",
    placeholder: "Mật khẩu",
    label: "Mật khẩu", // cho label vào thì sẽ thành input with label
  },
  ma_dly: {
    component: "select",
    name: "ma_dly",
    placeholder: "Mã đại lý",
    label: "Mã đại lý",
  },
  ngay_hl: {
    component: "date-picker",
    name: "ngay_hl",
    placeholder: "Ngày hiệu lực",
    label: "Ngày hiệu lực",
    rules: [ruleRequired],
  },
  ngay_kt: {
    component: "date-picker",
    name: "ngay_kt",
    placeholder: "Ngày kết thúc",
    label: "Ngày kết thúc",
    rules: [ruleRequired],
  },
  trang_thai: {
    component: "select",
    name: "trang_thai",
    placeholder: "Trạng thái",
    label: "Trạng thái ",
    rules: [ruleRequired],
  },
  ma_doi_tac: {
    component: "select",
    name: "ma_doi_tac",
    placeholder: "Đối tác",
    label: "Đối tác ",
    rules: [ruleRequired],
  },
  ngay_sinh: {
    component: "date-picker",
    name: "ngay_sinh",
    placeholder: "Ngày sinh",
    label: "Ngày sinh",
  },
  gioi_tinh: {
    component: "select",
    name: "gioi_tinh",
    placeholder: "Giới tính",
    label: "Giới tính",
  },
  cmt: {
    component: "input",
    name: "cmt",
    placeholder: "CMT/CCCD",
    label: "CMT/CCCD",
  },
};
export const TRANG_THAI_NGUOI_DUNG = [
  {ma: "D", ten: "Đang sử dụng"},
  {ma: "K", ten: "Ngừng sử dụng"},
];
export const GIOI_TINH = [
  {ma: "Nam", ten: "Nam"},
  {ma: "Nữ", ten: "Nữ"},
];
//tab phân quyền chức năng , phân quyền menu

//bảng phân quyền chức năng
export interface TableChucNangNguoiDungDataType {
  key: string;
  stt?: number;
  ma_chuc_nang?: string;
  ten?: string;
  loai?: string;
  // chon?: string;
  // hanh_dong?: string;
  is_checked: string;
  nhom: "";
}
export const phanQuyenChucNangColumns: TableProps<TableChucNangNguoiDungDataType>["columns"] = [
  {...defaultTableColumnsProps, title: "STT", dataIndex: "stt", key: "stt", width: colWidthByKey.sott},
  {...defaultTableColumnsProps, title: "Mã chức năng", dataIndex: "ma_chuc_nang", key: "ma_chuc_nang"},
  {title: "Tên chức năng", dataIndex: "ten", key: "ten", ...defaultTableColumnsProps},
  {title: "Loại", dataIndex: "loai", key: "loai", width: 80, ...defaultTableColumnsProps},
  {...defaultTableColumnsProps, title: "Chọn", dataIndex: "is_checked", key: "is_checked", width: 60},
];

export type DataIndexChucNang = keyof TableChucNangNguoiDungDataType;
//bảng phân quyền  menu
export interface TableMenuNguoiDungDataType {
  key: string;
  stt?: number;
  ma?: string;
  ten?: string;
  ma_doi_tac?: string;
  ma_cha?: string;
  url?: string;
  is_checked: string;
  nhom?: string;
}
export const phanQuyenMenuColumns: TableProps<TableMenuNguoiDungDataType>["columns"] = [
  {...defaultTableColumnsProps, title: "STT", dataIndex: "stt", key: "stt", width: colWidthByKey.sott},
  {title: "Mã menu", dataIndex: "ma", key: "ma", width: 150, ...defaultTableColumnsProps},
  {title: "Tên menu", dataIndex: "ten", key: "ten", ...defaultTableColumnsProps},
  {title: "Url", dataIndex: "url", key: "url", ...defaultTableColumnsProps},
  {title: "Nhóm", dataIndex: "nhom", key: "nhom", width: 70, ...defaultTableColumnsProps},
  {title: "Chọn", dataIndex: "is_checked", key: "is_checked", width: 60, ...defaultTableColumnsProps},
];

export type DataIndexMenu = keyof TableMenuNguoiDungDataType;
export const NHOM_MENU = [
  {label: "ADMIN", value: "ADMIN"},
  {label: "CLIENT", value: "CLIENT"},
];

//Tab đơn vị quản lý
export interface TableDonViQuanLyDataType {
  key: string;
  stt?: number;
  ten_chi_nhanh_ql?: string;
  ten_doi_tac_ql?: string;
  ma_chi_nhanh_ql?: string;
  ma_doi_tac_ql?: string;
  is_checked: string;
}
export const donViQuanLyColumns: TableProps<TableDonViQuanLyDataType>["columns"] = [
  {...defaultTableColumnsProps, title: "STT", dataIndex: "stt", key: "stt", width: colWidthByKey.sott},
  {title: "Đối tác quản lý", dataIndex: "ten_doi_tac_ql", key: "ten_doi_tac_ql", ...defaultTableColumnsProps},
  {title: "Chi nhánh quản lý", dataIndex: "ten_chi_nhanh_ql", key: "ten_chi_nhanh_ql", ...defaultTableColumnsProps},
  {title: "Chọn", dataIndex: "is_checked", key: "is_checked", width: 60, ...defaultTableColumnsProps},
];
export type DataIndexDVQL = keyof TableDonViQuanLyDataType;
//bảng phanaquyeenf vai trò
export interface TableVaiTroDataType {
  stt: number;
  ma: string;
  ten: string;
  trang_thai: string;
  trang_thai_ten: string;
  key: string;
}

// Giả định cột cho bảng vai trò
export const vaiTroColumns: TableProps<TableVaiTroDataType>["columns"] = [
  {...defaultTableColumnsProps, title: "STT", dataIndex: "stt", key: "stt", width: 60},
  {...defaultTableColumnsProps, title: "Mã vai trò", dataIndex: "ma", key: "ma", width: 200},
  {title: "Tên vai trò", dataIndex: "ten", key: "ten", ...defaultTableColumnsProps},
  // {...defaultTableColumnsProps, title: "Trạng thái", dataIndex: "trang_thai_ten", key: "trang_thai_ten", width: 130},
];
export type DataIndexVaiTro = keyof TableVaiTroDataType;
export interface IFormTimKiemPVaiTroChucNangFieldsConfig {
  // ma: IFormInput;
  ten: IFormInput;
}
export const FormTimKiemVaiTroChucNang: IFormTimKiemPVaiTroChucNangFieldsConfig = {
  ten: {
    component: "input",
    name: "ten",
    // label: " Tên vai trò",
    placeholder: "Tìm tên vai trò",
  },
};
export const radioItemTrangThaiVaiTroTable = [
  {value: "Đang sử dụng", text: "Đang sử dụng"},
  {value: "Ngừng sử dụng", text: "Ngừng sử dụng"},
];

//bảng phân quyền theo nhóm
export interface TableNhomDataType {
  stt: number;
  ma: string;
  ten: string;
  trang_thai: string;
  trang_thai_ten: string;
  key: string;
}

// Giả định cột cho bảng vai trò
export const nhomColumns: TableProps<TableNhomDataType>["columns"] = [
  {...defaultTableColumnsProps, title: "STT", dataIndex: "stt", key: "stt", width: 60},
  {...defaultTableColumnsProps, title: "Mã nhóm", dataIndex: "ma", key: "ma", width: 200},
  {title: "Tên nhóm", dataIndex: "ten", key: "ten", ...defaultTableColumnsProps},
  // {...defaultTableColumnsProps, title: "Trạng thái", dataIndex: "trang_thai_ten", key: "trang_thai_ten", width: 130},
];
export type DataIndexNhom = keyof TableNhomDataType;
export interface IFormTimKiemNhomChucNangFieldsConfig {
  // ma: IFormInput;
  ten: IFormInput;
}
export const FormTimKiemNhomChucNang: IFormTimKiemNhomChucNangFieldsConfig = {
  ten: {
    component: "input",
    name: "ten",
    // label: " Tên vai trò",
    placeholder: "Tìm tên nhóm",
  },
};
export const radioItemTrangThaiNhomTable = [
  {value: "Đang sử dụng", text: "Đang sử dụng"},
  {value: "Ngừng sử dụng", text: "Ngừng sử dụng"},
];
