import {IFormInput} from "@src/@types";
import {FormInput} from "@src/components";
import {ruleInputMessage} from "@src/hooks";
import {Col, Form, Row} from "antd";
import dayjs from "dayjs";
import {isEqual} from "lodash";
import {forwardRef, memo, useCallback, useEffect, useImperativeHandle, useMemo, useRef} from "react";
import {useHopDongConNguoiContext} from "../index.context";
import {
  FormTaoMoiHopDong,
  IModalTimCanBoKhaiThacRef,
  IModalTimCanBoRef,
  IModalTimDaiLyKhaiThacRef,
  IModalTimKhachHangRef,
  IThemHopDongStep1Ref,
  listHDVipSelect,
  listKieuHopDongSelect,
  ThemHopDongStep1Props,
} from "./Constant";
import {ModalTimCanBoHopDongConNguoi} from "./ThemHopDongStep1_ModalTimCanBoHopDongConNguoi";
import {ModalTimDaiLyKhaiThacHopDongConNguoi} from "./ThemHopDongStep1_ModalTimDaiLyKhaiThac";
import {ModalTimKhachHangHopDongConNguoi} from "./ThemHopDongStep1_ModalTimKhachHangHopDongConNguoi";
import {ModalTimCanBoKhaiThac} from "./ThemHopDongStep1_ModalTimCanBoKhaiThac";

const {ma_doi_tac_ql, ma_chi_nhanh_ql, phong_ql, ma_kh, so_hd, kieu_hd, so_hd_g, ma_cb_ql, ngay_cap, gio_hl, ngay_hl, gio_kt, ngay_kt, ma_sp, ma_ctbh, pt_kt, daily_kt, ma_nha_tpa, vip, ma_cb_kt} =
  FormTaoMoiHopDong;

const ThemHopDongStep1Component = forwardRef<IThemHopDongStep1Ref, ThemHopDongStep1Props>(({formThongTinHopDong, setIsChangeData}: ThemHopDongStep1Props, ref) => {
  useImperativeHandle(ref, () => ({}));

  const {listDoiTac, listChiNhanh, listSanPham, listChuongTrinhBaoHiem, listPhuongThucKhaiThac, listDonViBoiThuongTPA, listPhongBan, chiTietHopDong, getListCanBoQuanLy} = useHopDongConNguoiContext();

  const refModalTimKhachHang = useRef<IModalTimKhachHangRef>(null);
  const refModalTimCanBo = useRef<IModalTimCanBoRef>(null);
  const refModalTimDaiLyKhaiThac = useRef<IModalTimDaiLyKhaiThacRef>(null);
  const refModalTimCanBoKhaiThac = useRef<IModalTimCanBoKhaiThacRef>(null);
  //WATCH VALUE TỪ FORM
  const watchDoiTacCapDon = Form.useWatch("ma_doi_tac_ql", formThongTinHopDong);
  const watchChiNhanhCapDon = Form.useWatch("ma_chi_nhanh_ql", formThongTinHopDong);
  const watchKH = Form.useWatch("ma_kh", formThongTinHopDong);
  const watchCanBo = Form.useWatch("ma_cb_ql", formThongTinHopDong);
  const watchDaiLyKhaiThac = Form.useWatch("daily_kt", formThongTinHopDong);
  const watchPhongBan = Form.useWatch("phong_ql", formThongTinHopDong);
  const watchKieuHopDong = Form.useWatch("kieu_hd", formThongTinHopDong);
  const watchNgayCap = Form.useWatch("ngay_cap", formThongTinHopDong);
  const watchNgayHieuLuc = Form.useWatch("ngay_hl", formThongTinHopDong);
  const watchNgayKetThuc = Form.useWatch("ngay_kt", formThongTinHopDong);
  const watchCanBoKhaiThac = Form.useWatch("ma_cb_kt", formThongTinHopDong);

  //xử lý memo để không bị render lại nhiều lần
  const listChiNhanhSelect = useMemo(() => {
    return listChiNhanh.filter(item => item.ma_doi_tac === watchDoiTacCapDon);
  }, [listChiNhanh, watchDoiTacCapDon]);

  const listSanPhamSelect = useMemo(() => {
    return listSanPham.filter(item => item.ma_doi_tac_ql === watchDoiTacCapDon);
  }, [listSanPham, watchDoiTacCapDon]);

  const listChuongTrinhBaoHiemSelect = useMemo(() => {
    return listChuongTrinhBaoHiem.filter(item => item.ma_doi_tac_ql === watchDoiTacCapDon);
  }, [listChuongTrinhBaoHiem, watchDoiTacCapDon]);

  const listPhuongThucKhaiThacSelect = useMemo(() => {
    return listPhuongThucKhaiThac.filter(item => item.ma_doi_tac_ql === watchDoiTacCapDon);
  }, [listPhuongThucKhaiThac, watchDoiTacCapDon]);

  // const listDaiLyKhaiThacSelect = useMemo(() => {
  //   return listDaiLyKhaiThac.filter(item => item.ma_doi_tac_ql === watchDoiTacCapDon);
  // }, [listDaiLyKhaiThac, watchDoiTacCapDon]);

  const listDonViBoiThuongTPASelect = useMemo(() => {
    return listDonViBoiThuongTPA.filter(item => item.ma_doi_tac_ql === watchDoiTacCapDon);
  }, [listDonViBoiThuongTPA, watchDoiTacCapDon]);

  //list phòng ban đc filter theo đối tác và chi nhánh
  const listPhongBanSelect = useMemo(() => {
    return listPhongBan.filter(item => item.ma_doi_tac === watchDoiTacCapDon && item.ma_chi_nhanh === watchChiNhanhCapDon);
  }, [listPhongBan, watchDoiTacCapDon, watchChiNhanhCapDon]);

  //xử lý gọi API search cán bộ theo tên
  // const handleSearchCanBo = useCallback(
  //   async (searchValue: string) => {
  //     getListCanBoQuanLy({
  //       ten: searchValue,
  //       ma_doi_tac_ql: watchDoiTacCapDon,
  //       // ma_chi_nhanh_ql: watchChiNhanhCapDon,
  //       // phong_ql: watchPhongBan,
  //     });
  //   },
  //   [getListCanBoQuanLy, watchDoiTacCapDon],
  // );
  // const deboundSearchCanBoQuanLy = useMemo(() => debounce(handleSearchCanBo, 500), [handleSearchCanBo]); //cái này để xử lý delay 0,5s rồi mới gọi api search

  //SET ERROR FORM
  const setErrorFormFields = useCallback(
    (name: string, errors: string[]) => {
      formThongTinHopDong.setFields([
        {
          name,
          errors,
        },
      ]);
    },
    [formThongTinHopDong],
  );

  //XỬ LÝ ĐIỀU KIỆN ĐỂ MỞ SELECT
  const checkDieuKienMoSelect = (inputName: string, isOpen: boolean) => {
    //NẾU CHƯA CÓ DATA ĐỐI TÁC MÀ CHỌN CÁC INPUT TƯƠNG ỨNG -> HIỂN THỊ LỖI
    const arrInputNameTheoDoiTac: Array<string> = ["ma_chi_nhanh_ql", "ma_ctbh", "pt_kt", "daily_kt", "ma_cb_ql", "phong_ql", "ma_kh", "ma_sp"];
    const arrInputNameTheoChiNhanh: Array<string> = ["phong_ql", "ma_kh"];
    const arrInputNameTheoNgayHieuLuc: Array<string> = ["ma_sp"];
    if (arrInputNameTheoDoiTac.includes(inputName) && isOpen && !watchDoiTacCapDon) setErrorFormFields("ma_doi_tac_ql", ["Vui lòng chọn đối tác cấp đơn"]);
    if (arrInputNameTheoChiNhanh.includes(inputName) && isOpen && !watchChiNhanhCapDon) setErrorFormFields("ma_chi_nhanh_ql", ["Vui lòng chọn chi nhánh cấp đơn"]);
    if (arrInputNameTheoNgayHieuLuc.includes(inputName) && isOpen && !watchNgayHieuLuc) setErrorFormFields("ngay_hl", ["Vui lòng chọn ngày hiệu lực"]);
  };

  //XỬ LÝ LOGIC VALIDATE NGÀY CẤP, NGÀY HIỆU LỰC, NGÀY KẾT THÚC
  useEffect(() => {
    //phải thêm timout vào thì mới ăn setFormFields
    if (watchNgayCap && watchNgayHieuLuc) {
      setTimeout(() => {
        setErrorFormFields("ngay_cap", dayjs(watchNgayCap).isAfter(watchNgayHieuLuc) ? ["Ngày cấp phải trước ngày hiệu lực"] : []);
      }, 350);
    }
    if (watchNgayCap && watchNgayKetThuc) {
      setTimeout(() => {
        setErrorFormFields("ngay_cap", dayjs(watchNgayCap).isAfter(watchNgayKetThuc) ? ["Ngày cấp phải trước ngày kết thúc"] : []);
      }, 350);
    }
    if (watchNgayHieuLuc && watchNgayKetThuc) {
      setTimeout(() => {
        setErrorFormFields("ngay_hl", dayjs(watchNgayHieuLuc).isAfter(watchNgayKetThuc) ? ["Ngày hiệu lực trước ngày kết thúc"] : []);
      }, 350);
    }
  }, [watchNgayCap, watchNgayHieuLuc, watchNgayKetThuc, setErrorFormFields]);
  const handleValuesChange = useCallback(
    (changedValues: any, allValues: any) => {
      setIsChangeData(true);
    },
    [setIsChangeData],
  );

  // RENDER
  const renderFormInputColum = (props: IFormInput, span = 5) => {
    return (
      <Col span={span}>
        <FormInput {...props} />
      </Col>
    );
  };
  return (
    <>
      <Form form={formThongTinHopDong} layout="vertical" className="mt-5" initialValues={{}} onValuesChange={handleValuesChange}>
        {/* gutter : khoảng cách giữa các ô */}
        <Row gutter={16}>
          {renderFormInputColum({
            ...ma_doi_tac_ql,
            options: listDoiTac,
            disabled: chiTietHopDong ? true : false,
            onChange: value => {
              //clear các input phụ thuộc vào đối tác
              formThongTinHopDong.setFieldValue("ma_chi_nhanh_ql", undefined);
              formThongTinHopDong.setFieldValue("phong_ql", undefined);
              formThongTinHopDong.setFieldValue("ma_ctbh", undefined);
              formThongTinHopDong.setFieldValue("pt_kt", undefined);
              formThongTinHopDong.setFieldValue("daily_kt", undefined);
              formThongTinHopDong.setFieldValue("ma_cb_kt", undefined);
              formThongTinHopDong.setFieldValue("ma_cb_ql", undefined);
              // getListCanBoQuanLy({ma_doi_tac_ql: value}); //do cán bộ quàn lý nhiều quá > 1000 dòng nên cần gọi API để filter theo ma_doi_tac_ql
            },
          })}
          {renderFormInputColum({
            ...ma_chi_nhanh_ql,
            options: listChiNhanhSelect,
            disabled: chiTietHopDong ? true : false,
            onDropdownVisibleChange: open => checkDieuKienMoSelect(ma_chi_nhanh_ql.name, open),
            onChange: value => {
              formThongTinHopDong.setFieldValue("phong_ql", undefined); //chi nhánh change -> clear phòng đã được select
              // getListCanBoQuanLy({ma_doi_tac_ql: watchDoiTacCapDon, ma_chi_nhanh_ql: value});
              // formThongTinHopDong.setFieldValue("ma_cb_ql", undefined);
            },
          })}
          {renderFormInputColum({...phong_ql, options: listPhongBanSelect, onDropdownVisibleChange: open => checkDieuKienMoSelect(phong_ql.name, open)})}
          {renderFormInputColum(
            {
              ...ma_kh,
              options: [{ten: watchKH?.ten, ma: watchKH?.ma}],
              onDropdownVisibleChange: open => checkDieuKienMoSelect(ma_kh.name, open),
              onClick: () => {
                if (watchDoiTacCapDon && watchChiNhanhCapDon) refModalTimKhachHang.current?.open(watchKH ? {ten: watchKH?.ten, ma: watchKH?.ma} : undefined);
              },
            },
            9,
          )}
        </Row>

        <Row gutter={16}>
          {renderFormInputColum(ngay_cap)}
          {renderFormInputColum(gio_hl)}
          {renderFormInputColum({
            ...ngay_hl,
            minDate: watchNgayCap,
            onChange: date => {
              //ngày kết thúc = ngày hiệu lực + 1
              formThongTinHopDong.setFields([
                {
                  name: "ngay_kt",
                  value: dayjs(date).add(1, "y"),
                },
              ]);
            },
          })}
          {renderFormInputColum(gio_kt, 4)}
          {renderFormInputColum({...ngay_kt, minDate: watchNgayHieuLuc})}
        </Row>

        <Row gutter={16}>
          {renderFormInputColum({...ma_sp, options: listSanPhamSelect, onDropdownVisibleChange: open => checkDieuKienMoSelect(ma_sp.name, open)})}
          {renderFormInputColum({...so_hd, disabled: chiTietHopDong ? true : false})}
          {renderFormInputColum({
            ...kieu_hd,
            options: listKieuHopDongSelect,
            onChange: value => {
              if (value === "G") {
                formThongTinHopDong.setFields([
                  {
                    name: "so_hd_g",
                    value: "",
                  },
                ]);
              }
            },
          })}
          {renderFormInputColum({...so_hd_g, disabled: watchKieuHopDong === "G" ? true : false, rules: watchKieuHopDong === "G" ? [] : [ruleInputMessage.required]}, 4)}
          {renderFormInputColum({
            ...ma_cb_ql,
            options: [{ten: watchCanBo?.ten, ma: watchCanBo?.ma}],
            onClick: () => {
              if (watchDoiTacCapDon) refModalTimCanBo.current?.open(watchCanBo ? {ten: watchCanBo?.ten, ma: watchCanBo?.ma} : undefined);
            },
            // onSearch: deboundSearchCanBoQuanLy,
            onDropdownVisibleChange: open => checkDieuKienMoSelect(ma_cb_ql.name, open),
          })}
        </Row>

        <Row gutter={16}>
          {renderFormInputColum({
            ...ma_ctbh,
            options: listChuongTrinhBaoHiemSelect.filter(item => (item.ngay_ad_tim ?? 0) < +dayjs(watchNgayHieuLuc).format("YYYYMMDD")),
            onDropdownVisibleChange: open => checkDieuKienMoSelect(ma_sp.name, open),
          })}
          {renderFormInputColum({...pt_kt, options: listPhuongThucKhaiThacSelect, onDropdownVisibleChange: open => checkDieuKienMoSelect(pt_kt.name, open)})}
          {renderFormInputColum(
            {
              ...daily_kt,
              options: [{ten: watchDaiLyKhaiThac?.ten, ma: watchDaiLyKhaiThac?.ma}],
              onDropdownVisibleChange: open => checkDieuKienMoSelect(daily_kt.name, open),
              onClick: () => {
                if (watchDoiTacCapDon) refModalTimDaiLyKhaiThac.current?.open(watchDaiLyKhaiThac ? {ten: watchDaiLyKhaiThac?.ten, ma: watchDaiLyKhaiThac?.ma} : undefined);
              },
            },
            4,
          )}
          {renderFormInputColum(
            {
              ...ma_cb_kt,
              options: [{ten: watchCanBoKhaiThac?.ten, ma: watchCanBoKhaiThac?.ma}],
              onClick: () => {
                console.log("watchCanBoKhaiThac", watchCanBoKhaiThac);
                
                if (watchDoiTacCapDon) refModalTimCanBoKhaiThac.current?.open(watchCanBoKhaiThac ? {ten: watchCanBoKhaiThac?.ten, ma: watchCanBoKhaiThac?.ma} : undefined);
              },
            },
            3,
          )}
          {renderFormInputColum({...ma_nha_tpa, options: listDonViBoiThuongTPASelect}, 4)}
          {renderFormInputColum({...vip, options: listHDVipSelect}, 3)}
        </Row>
      </Form>

      <ModalTimKhachHangHopDongConNguoi
        ref={refModalTimKhachHang}
        maDoiTacSelected={watchDoiTacCapDon}
        maChiNhanhSelected={watchChiNhanhCapDon}
        listChiNhanhSelect={listChiNhanhSelect}
        onSelectKhachHang={dataKhachang => formThongTinHopDong.setFieldValue("ma_kh", dataKhachang)}
      />

      <ModalTimCanBoHopDongConNguoi
        ref={refModalTimCanBo}
        maDoiTacSelected={watchDoiTacCapDon}
        listChiNhanhSelect={listChiNhanhSelect}
        maPhongBanSelected={watchPhongBan}
        listPhongBanSelect={listPhongBanSelect}
        onSelectCanBo={dataCanBo => formThongTinHopDong.setFieldValue("ma_cb_ql", dataCanBo)}
      />

      <ModalTimDaiLyKhaiThacHopDongConNguoi
        ref={refModalTimDaiLyKhaiThac}
        maDoiTacSelected={watchDoiTacCapDon}
        onSelectDaiLyKhaiThac={dataDaiLy => formThongTinHopDong.setFieldValue("daily_kt", dataDaiLy)}
      />

      <ModalTimCanBoKhaiThac
        ref={refModalTimCanBoKhaiThac}
        maDoiTacSelected={watchDoiTacCapDon}
        daiLyKhaiThacSelected={watchDaiLyKhaiThac?.ma}
        onSelectCanBoKhaiThac={dataCanBo => formThongTinHopDong.setFieldValue("ma_cb_kt", dataCanBo)}
      />
    </>
  );
});

ThemHopDongStep1Component.displayName = "ThemHopDongStep1Component";
export const ThemHopDongStep1 = memo(ThemHopDongStep1Component, isEqual);
